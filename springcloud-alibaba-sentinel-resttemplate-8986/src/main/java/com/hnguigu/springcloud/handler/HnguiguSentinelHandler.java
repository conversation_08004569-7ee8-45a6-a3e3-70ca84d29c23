package com.hnguigu.springcloud.handler;

import com.alibaba.cloud.sentinel.rest.SentinelClientHttpResponse;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Slf4j
@Component
public class HnguiguSentinelHandler {

    /*public static String handleFallback(Long id, Throwable e) {
        log.error("fallback执行了...,异常信息:{}", e.getMessage());
        return "fallback";
    }

    public static String handleBlock(Long id, BlockException e) {
        log.error("block执行了...,异常信息:{}", e.getMessage());
        return "fallback";
    }*/

    /**
     * 流控
     * @param request
     * @param body
     * @param execution
     * @param ex
     * @return
     */
    public static SentinelClientHttpResponse handleBlock(HttpRequest request, byte[] body,
                                                         ClientHttpRequestExecution execution,
                                                         BlockException ex) {
        String message = ex.getMessage();
        if (StringUtils.isEmpty(message)) {
            message = "流控产生了，请重试！";
        }
        log.error("block执行了...,异常信息:{}", message);

        SentinelClientHttpResponse response = new SentinelClientHttpResponse(message);
        return response;
    }

    /**
     * 降级（异常）
     * @param request
     * @param body
     * @param execution
     * @param ex
     * @return
     */
    public static SentinelClientHttpResponse handleFallback(HttpRequest request, byte[] body,
                                                            ClientHttpRequestExecution execution,
                                                            BlockException ex) {
        String message = ex.getMessage();
        if (StringUtils.isEmpty(message)) {
            message = "降级产生了，请重试！";
        }
        log.error("fallback执行了...,异常信息:{}", message);
        SentinelClientHttpResponse response = new SentinelClientHttpResponse(message);
        return response;
    }
}
